// SurveyCompletionPage.module.scss

// Variables
$primary-color: #2563eb;
$success-color: #10b981;
$secondary-color: #64748b;
$background-color: #f8fafc;
$white: #ffffff;
$gray-50: #f9fafb;
$gray-100: #f3f4f6;
$gray-200: #e5e7eb;
$gray-300: #d1d5db;
$gray-400: #9ca3af;
$gray-500: #6b7280;
$gray-600: #4b5563;
$gray-700: #374151;
$gray-800: #1f2937;

$border-radius: 8px;
$border-radius-lg: 12px;
$shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

// Main Container
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, $background-color 0%, #e2e8f0 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.completionCard {
  background: $white;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-lg;
  max-width: 600px;
  width: 100%;
  overflow: hidden;
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Header Section
.header {
  text-align: center;
  padding: 3rem 2rem 2rem;
  background: linear-gradient(135deg, $primary-color 0%, #1d4ed8 100%);
  color: $white;
  position: relative;
}

// Header for next step state (less "complete" feeling)
.headerNextStep {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.logoContainer {
  margin-bottom: 1.5rem;
}

.fourkitesLogo {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: $white;
  padding: 8px;
}

.successIcon {
  margin-bottom: 1.5rem;
}

.checkmarkContainer {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: $success-color;
  border-radius: 50%;
  animation: checkmarkPulse 0.8s ease-out;
}

@keyframes checkmarkPulse {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.checkmark {
  width: 32px;
  height: 32px;
  color: $white;
}

// Next Step Icon (for new companies)
.nextStepIconContainer {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: #f59e0b;
  border-radius: 50%;
  animation: nextStepPulse 1.5s ease-in-out infinite;
}

@keyframes nextStepPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.7);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(245, 158, 11, 0);
  }
}

.nextStepIcon {
  width: 32px;
  height: 32px;
  color: $white;
}

.title {
  font-size: 2.25rem;
  font-weight: 700;
  margin: 0 0 0.5rem;
  line-height: 1.2;
}

.subtitle {
  font-size: 1.125rem;
  opacity: 0.9;
  margin: 0;
  font-weight: 400;
}

// Content Section
.content {
  padding: 2rem;
}

.thankYouSection {
  margin-bottom: 2.5rem;
  text-align: center;
}

.sectionTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: $gray-800;
  margin: 0 0 1rem;
}

.description {
  font-size: 1rem;
  color: $gray-600;
  line-height: 1.6;
  margin: 0;
}

// What's Next Section
.whatsNextSection {
  margin-bottom: 2.5rem;
}

.sectionSubtitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: $gray-800;
  margin: 0 0 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.nextStepsList {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.nextStepItem {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
}

.stepIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: $primary-color;
  color: $white;
  border-radius: 50%;
  font-weight: 600;
  font-size: 0.875rem;
  flex-shrink: 0;
}

.stepContent {
  flex: 1;

  h4 {
    font-size: 1rem;
    font-weight: 600;
    color: $gray-800;
    margin: 0 0 0.25rem;
  }

  p {
    font-size: 0.875rem;
    color: $gray-600;
    line-height: 1.5;
    margin: 0;
  }
}

// Urgent Action Section (for new companies)
.urgentActionSection {
  margin-bottom: 2rem;
}

.urgentActionCard {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border: 2px solid #f59e0b;
  border-radius: $border-radius-lg;
  padding: 1.5rem;
  text-align: center;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.15);
}

.urgentActionHeader {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.urgentActionIcon {
  font-size: 1.5rem;
}

.urgentActionTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: #92400e;
  margin: 0;
}

.urgentActionDescription {
  font-size: 1rem;
  color: #92400e;
  margin: 0 0 1.5rem;
  line-height: 1.5;
}

.urgentActionSteps {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  text-align: left;
  max-width: 400px;
  margin: 0 auto;
}

.actionStep {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.stepNumber {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: #f59e0b;
  color: $white;
  border-radius: 50%;
  font-size: 0.875rem;
  font-weight: 600;
  flex-shrink: 0;
}

.stepText {
  font-size: 0.875rem;
  color: #92400e;
  font-weight: 500;
}

// Action Section
.actionSection {
  margin-bottom: 2rem;
}

.nextStepCard {
  background: $gray-50;
  border: 1px solid $gray-200;
  border-radius: $border-radius;
  padding: 1.5rem;
  text-align: center;
}

.nextStepHeader {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  margin-bottom: 1rem;

  h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: $gray-800;
    margin: 0;
  }
}

.nextStepIcon {
  font-size: 1.5rem;
}

.nextStepDescription {
  font-size: 0.875rem;
  color: $gray-600;
  line-height: 1.5;
  margin: 0 0 1.5rem;
}

// Auto-redirect
.autoRedirectNotice {
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: $border-radius;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.countdownText {
  font-size: 0.875rem;
  color: #92400e;
  margin: 0 0 0.5rem;
  font-weight: 500;
}

.cancelAutoRedirect {
  background: none;
  border: none;
  color: #92400e;
  text-decoration: underline;
  cursor: pointer;
  font-size: 0.75rem;
  padding: 0;

  &:hover {
    color: #78350f;
  }
}

// Buttons
.buttonContainer {
  display: flex;
  justify-content: center;
}

.primaryButton {
  min-width: 200px;
  font-weight: 600;
}

.urgentButton {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
  border: none !important;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3) !important;
  animation: buttonPulse 2s ease-in-out infinite;

  &:hover {
    background: linear-gradient(135deg, #d97706 0%, #b45309 100%) !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(245, 158, 11, 0.4) !important;
  }
}

@keyframes buttonPulse {
  0%, 100% {
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
  }
  50% {
    box-shadow: 0 6px 20px rgba(245, 158, 11, 0.5);
  }
}

// Additional Information
.additionalInfoSection {
  margin-bottom: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.infoCard {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #0ea5e9;
  border-radius: $border-radius;
  padding: 1.25rem;
}

.infoTitle {
  font-size: 1rem;
  font-weight: 600;
  color: $gray-800;
  margin: 0 0 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.infoText {
  font-size: 0.875rem;
  color: $gray-600;
  line-height: 1.5;
  margin: 0;
}

.supportInfo {
  background: #f0fdf4;
  border: 1px solid #22c55e;
  border-radius: $border-radius;
  padding: 1.25rem;
}

.supportTitle {
  font-size: 1rem;
  font-weight: 600;
  color: $gray-800;
  margin: 0 0 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.supportText {
  font-size: 0.875rem;
  color: $gray-600;
  line-height: 1.5;
  margin: 0;
}

// Contact & Footer
.contactSection {
  text-align: center;
  margin-bottom: 1.5rem;
}

.contactText {
  font-size: 0.875rem;
  color: $gray-600;
  margin: 0;
}

.footer {
  text-align: center;
  padding-top: 1.5rem;
  border-top: 1px solid $gray-200;
}

.footerText {
  font-size: 0.75rem;
  color: $gray-500;
  margin: 0 0 0.25rem;
}

.footerSubtext {
  font-size: 0.625rem;
  color: $gray-400;
  margin: 0;
  font-style: italic;
}

// Responsive Design
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .header {
    padding: 2rem 1.5rem 1.5rem;
  }

  .title {
    font-size: 1.875rem;
  }

  .content {
    padding: 1.5rem;
  }

  .nextStepItem {
    flex-direction: column;
    gap: 0.75rem;
    text-align: center;
  }

  .stepIcon {
    align-self: center;
  }

  .additionalInfoSection {
    gap: 1rem;
  }

  .infoCard,
  .supportInfo {
    padding: 1rem;
  }

  .primaryButton {
    min-width: 180px;
    width: 100%;
  }

  .nextStepCard {
    padding: 1.25rem;
  }
}
