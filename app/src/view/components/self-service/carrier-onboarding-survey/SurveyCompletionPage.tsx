import React from 'react';
import { useTranslation } from 'react-i18next';
import styles from './SurveyCompletionPage.module.scss';
import fourkitesCircleLogo from "assets/img/fourkitesCircleLogo.png";
import { Button } from "@fourkites/elemental-atoms";
import { onNav } from "router/navigationUtils";
import { fourkitesUrls, FOURKITES_APP_URL } from "api/http/apiUtils";

interface SurveyCompletionPageProps {
  shipperName: string;
  companyAlreadyExists: boolean;
  onboardingToken?: string;
  carrierName?: string;
  contactEmail?: string;
}

const SurveyCompletionPage: React.FC<SurveyCompletionPageProps> = ({
  shipperName,
  companyAlreadyExists,
  onboardingToken,
  carrierName = "Carrier",
  contactEmail
}) => {
  const { t } = useTranslation();

  const handlePrimaryAction = () => {
    if (companyAlreadyExists) {
      // Company already exists, redirect to login page
      onNav(fourkitesUrls.login);
    } else {
      // New company, redirect to onboarding URL with token
      if (onboardingToken) {
        onNav(`${FOURKITES_APP_URL}self-service/onboarding/${onboardingToken}`);
      } else {
        // Fallback to login if no token
        onNav(fourkitesUrls.login);
      }
    }
  };



  const getNextStepInfo = () => {
    if (companyAlreadyExists) {
      return {
        title: t("Ready to Access Your Account"),
        description: t("Your company already has a FourKites account. You can now sign in to access your dashboard and manage your shipments."),
        buttonText: t("Login to FourKites"),
        buttonIcon: "🔐"
      };
    } else {
      return {
        title: t("Complete Your Account Setup"),
        description: t("We'll now guide you through setting up your FourKites account, including user credentials and initial configuration."),
        buttonText: t("Continue with Onboarding"),
        buttonIcon: "🚀"
      };
    }
  };

  const nextStepInfo = getNextStepInfo();

  return (
    <div className={styles.container}>
      <div className={styles.completionCard}>
        {/* Header Section */}
        <div className={`${styles.header} ${!companyAlreadyExists ? styles.headerNextStep : ''}`}>
          <div className={styles.logoContainer}>
            <img
              src={fourkitesCircleLogo}
              alt="FourKites Logo"
              className={styles.fourkitesLogo}
            />
          </div>

          {/* Icon - Different for completion vs next step */}
          <div className={styles.successIcon}>
            {companyAlreadyExists ? (
              <div className={styles.checkmarkContainer}>
                <svg className={styles.checkmark} viewBox="0 0 24 24" fill="none">
                  <path
                    d="M9 12l2 2 4-4"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </div>
            ) : (
              <div className={styles.nextStepIconContainer}>
                <svg className={styles.nextStepIcon} viewBox="0 0 24 24" fill="none">
                  <path
                    d="M5 12h14m-7-7l7 7-7 7"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </div>
            )}
          </div>

          <h1 className={styles.title}>
            {companyAlreadyExists
              ? t("Survey Complete!")
              : t("Survey Complete!")
            }
          </h1>

          <p className={styles.subtitle}>
            {companyAlreadyExists
              ? t("Thank you for completing the carrier onboarding survey")
              : t("Thank you for completing the survey - Let's set up your account")
            }
          </p>
        </div>

        {/* Content Section */}
        <div className={styles.content}>
          {/* Thank You Message */}
          <div className={styles.thankYouSection}>
            <p className={styles.description}>
              {companyAlreadyExists
                ? t("Thank you for completing the survey! We'll be in touch soon with next steps for your integration with {{shipperName}}.", { shipperName })
                : t("Thank you! Your next step is to complete your registration. This is required to create your FourKites account, which you'll need to log in and continue the onboarding process.")
              }
            </p>
          </div>

          {/* Enhanced Next Step Section for New Companies */}
          {!companyAlreadyExists && (
            <div className={styles.urgentActionSection}>
              <div className={styles.urgentActionCard}>
                <div className={styles.urgentActionHeader}>
                  <span className={styles.urgentActionIcon}>→</span>
                  <h3 className={styles.urgentActionTitle}>
                    {t("Next Steps")}
                  </h3>
                </div>
                <p className={styles.urgentActionDescription}>
                  {t("To complete your setup and start using FourKites, we'll need to create your account.")}
                </p>
                <div className={styles.urgentActionSteps}>
                  <div className={styles.actionStep}>
                    <span className={styles.stepNumber}>1</span>
                    <span className={styles.stepText}>{t("Click 'Continue with Onboarding' below")}</span>
                  </div>
                  <div className={styles.actionStep}>
                    <span className={styles.stepNumber}>2</span>
                    <span className={styles.stepText}>{t("Fill out your account details")}</span>
                  </div>
                  <div className={styles.actionStep}>
                    <span className={styles.stepNumber}>3</span>
                    <span className={styles.stepText}>{t("You're all set up in FourKites")}</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Next Step Action */}
          <div className={styles.actionSection}>
            {/* Action Buttons */}
            <div className={styles.buttonContainer}>
              <Button
                size="large"
                theme="primary"
                onClick={handlePrimaryAction}
                className={`${styles.primaryButton} ${!companyAlreadyExists ? styles.urgentButton : ''}`}
              >
                {nextStepInfo.buttonText}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SurveyCompletionPage;
